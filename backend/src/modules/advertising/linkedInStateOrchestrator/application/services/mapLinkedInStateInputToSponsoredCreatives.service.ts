import { IConversationCallToActionCopyRepository } from "../../../application/interfaces/infrastructure/repositories/conversationCallToActionCopy.repository.interface";
import { ILinkedInAdAccountRepository } from "../../../application/interfaces/infrastructure/repositories/linkedInAdAccount.repository.interface";
import { LinkedInAdProgramRepositoryInterface } from "../../../application/interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../../application/interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { ILinkedInPostRepositoryInterface } from "../../../application/interfaces/infrastructure/repositories/linkedInPost.repository.interface";
import { ILinkedInSponsoredCreativeRepository } from "../../../application/interfaces/infrastructure/repositories/linkedInSponsoredCreative.repository.interface";
import { ILinkedInService } from "../../../application/interfaces/infrastructure/services/thirdPartyApis/linkedInApi/linkedIn.service.interface";
import {
  CarouselImageLinkedInPost,
  DocumentLinkedInPost,
  SingleImageLinkedInPost,
  SingleVideoLinkedInPost,
} from "../../../domain/entites/linkedInPost";
import {
  SponsoredInmailLinkedInStateInput,
  SponsoredPostLinkedInStateInput,
} from "../../domain/linkedInStateInput.valueObject";
import { mapLinkedInStateInputToSponsoredCreatives } from "../../domain/mapLinkedInStateInputToSponsoredCreatives.domain";
import { ILinkedInStateConfigRepository } from "../../repository/linkedInStateConfig.repository.interface";

export class MapLinkedInStateInputToSponsoredCreativesService {
  constructor(
    private readonly ctx: {
      linkedInPostRepository: ILinkedInPostRepositoryInterface;
      linkedInSponsoredCreativeRepository: ILinkedInSponsoredCreativeRepository;
      linkedInCampaignRepository: ILinkedInCampaignRepositoryInterface;
      linkedInConversationCallToActionRepository: IConversationCallToActionCopyRepository;
      linkedInStateConfigRepository: ILinkedInStateConfigRepository;
      linkedInService: ILinkedInService;
      linkedInAdAccountRepository: ILinkedInAdAccountRepository;
      adSegmentRepository: ILinkedInAdSegmentRepository;
      adProgramRepository: LinkedInAdProgramRepositoryInterface;
    },
  ) {}

  async setupLinkedInStateForSponsoredInmail(
    input: SponsoredInmailLinkedInStateInput,
  ) {
    const conversationCallToActions =
      await this.ctx.linkedInConversationCallToActionRepository.getAllForAdSegment(
        input.adSegmentId,
      );
    const sponsoredCreatives =
      await this.ctx.linkedInSponsoredCreativeRepository.getManyForAdSegment(
        input.adSegmentId,
      );

    const mappedLinkedInStateForSponsoredInmail =
      mapLinkedInStateInputToSponsoredCreatives.forSponsoredInmail({
        linkedInStateInput: input,
        conversationCallToAction: conversationCallToActions,
        sponsoredCreatives: sponsoredCreatives,
      });
    if (mappedLinkedInStateForSponsoredInmail.isErr()) {
      throw new Error("Error mapping linkedInStateForSponsoredInmail");
    }

    return mappedLinkedInStateForSponsoredInmail.value;
  }

  async setupLinkedInStateForSponsoredContent(
    input: SponsoredPostLinkedInStateInput,
  ) {
    let linkedInPosts:
      | {
          type: "SINGLE_IMAGE";
          content: SingleImageLinkedInPost[];
        }
      | {
          type: "SINGLE_VIDEO";
          content: SingleVideoLinkedInPost[];
        }
      | {
          type: "CAROUSEL_IMAGE";
          content: CarouselImageLinkedInPost[];
        }
      | {
          type: "DOCUMENT";
          content: DocumentLinkedInPost[];
        }
      | null = null;

    switch (input.adFormat) {
      case "SINGLE_IMAGE":
        linkedInPosts = {
          type: "SINGLE_IMAGE",
          content:
            await this.ctx.linkedInPostRepository.getSingleImagePostsForAdSegment(
              {
                adSegmentId: input.adSegmentId,
              },
            ),
        };
        break;
      case "SINGLE_VIDEO":
        linkedInPosts = {
          type: "SINGLE_VIDEO",
          content:
            await this.ctx.linkedInPostRepository.getSingleVideoPostsForAdSegment(
              {
                adSegmentId: input.adSegmentId,
              },
            ),
        };
        break;
      case "DOCUMENT":
        linkedInPosts = {
          type: "DOCUMENT",
          content:
            await this.ctx.linkedInPostRepository.getSingleDocumentPostsForAdSegment(
              {
                adSegmentId: input.adSegmentId,
              },
            ),
        };
        break;
      default:
        throw new Error("Invalid ad format");
    }
    const linkedInSponsoredCreatives =
      await this.ctx.linkedInSponsoredCreativeRepository.getManyForAdSegment(
        input.adSegmentId,
      );

    const mappedLinkedInSponsoredCreatives =
      mapLinkedInStateInputToSponsoredCreatives.forSponsoredContent({
        linkedInPosts: linkedInPosts.content,
        linkedInStateInput: input,
        sponsoredCreatives: linkedInSponsoredCreatives,
      });

    if (mappedLinkedInSponsoredCreatives.isErr()) {
      throw new Error("Error mapping linkedInSponsoredCreatives");
    }

    return mappedLinkedInSponsoredCreatives.value;
  }
}
