import { z } from "zod";

import { abTestStatusEnum } from "../../../../../database/schemas/advertising/abTests/_baseAbTestTableSchemaUtils";
import { abTestTypeSchema } from "../domain/abTestType.valueObject";

const baseAbTestRoundDataDtoSchema = z.object({
  abTestId: z.string().uuid(),
  type: abTestTypeSchema,
});

const sponsoredContentVarientsMap = z.object({
  adFormatType: z.literal("SPONSORED_CONTENT"),
  audienceType: z.string(),
  valuePropType: z.string(),
  creativeType: z.string(),
  socialPostBodyCopyType: z.string(),
  socialPostCallToActionType: z.string(),
});

const sponsoredInmailVarientsMap = z.object({
  adFormatType: z.literal("SPONSORED_INMAIL"),
  audienceType: z.string(),
  valuePropType: z.string(),
  conversationSubjectCopyType: z.string(),
  conversationMessageCopyType: z.string(),
  conversationCallToActionType: z.string(),
});

const varientsMap = z.discriminatedUnion("adFormatType", [
  sponsoredContentVarientsMap,
  sponsoredInmailVarientsMap,
]);

export type VarientMap = z.infer<typeof varientsMap>;

const currentRunningAbTestRoundDataDtoSchema =
  baseAbTestRoundDataDtoSchema.extend({
    status: z.literal("IN_PROGRESS"),
    currentBest: z.object({
      sponsoredCreativeId: z.string().uuid(),
      varientId: z.string().uuid(),
      adId: z.string(),
      adName: z.string(),
      varientsUsedInSponsoredCreative: varientsMap,
      metrics: z.object({
        impressions: z.number().nullable(),
        clicks: z.number().nullable(),
        conversions: z.number().nullable(),
        leads: z.number().nullable(),
        videoViews: z.number().nullable(),
        sends: z.number().nullable(),
        opens: z.number().nullable(),
        cost: z.number().nullable(),
        actionClicks: z.number().nullable(),
        totalEngagements: z.number().nullable(),
        oneClickLeadFormOpens: z.number().nullable(),
        landingPageClicks: z.number().nullable(),
        videoCompletions: z.number().nullable(),
        videoFirstQuartileCompletions: z.number().nullable(),
        videoMidpointCompletions: z.number().nullable(),
        videoThirdQuartileCompletions: z.number().nullable(),
        videoStarts: z.number().nullable(),
        externalWebsiteConversions: z.number().nullable(),
      }),
    }),
    contender: z.object({
      sponsoredCreativeId: z.string().uuid(),
      varientId: z.string().uuid(),
      adId: z.string(),
      adName: z.string(),
      varientsUsedInSponsoredCreative: varientsMap,
      metrics: z.object({
        impressions: z.number().nullable(),
        clicks: z.number().nullable(),
        conversions: z.number().nullable(),
        leads: z.number().nullable(),
        videoViews: z.number().nullable(),
        sends: z.number().nullable(),
        opens: z.number().nullable(),
        cost: z.number().nullable(),
        actionClicks: z.number().nullable(),
        totalEngagements: z.number().nullable(),
        oneClickLeadFormOpens: z.number().nullable(),
        landingPageClicks: z.number().nullable(),
        videoCompletions: z.number().nullable(),
        videoFirstQuartileCompletions: z.number().nullable(),
        videoMidpointCompletions: z.number().nullable(),
        videoThirdQuartileCompletions: z.number().nullable(),
        videoStarts: z.number().nullable(),
        externalWebsiteConversions: z.number().nullable(),
      }),
    }),
    winner: z.enum(["CURRENT_BEST", "CONTENDER"]).nullable(),
  });

const pastAbTestRoundDataDtoSchema = baseAbTestRoundDataDtoSchema.extend({
  status: z.enum(["COMPLETED", "AUTO_RESOLVED", "USER_RESOLVED", "CANCELLED"]),
  currentBest: z.object({
    sponsoredCreativeId: z.string().uuid(),
    varientId: z.string().uuid(),
    varientsUsedInSponsoredCreative: varientsMap,
    metrics: z.object({
      impressions: z.number().nullable(),
      clicks: z.number().nullable(),
      conversions: z.number().nullable(),
      leads: z.number().nullable(),
      videoViews: z.number().nullable(),
      sends: z.number().nullable(),
      opens: z.number().nullable(),
      cost: z.number().nullable(),
      actionClicks: z.number().nullable(),
      totalEngagements: z.number().nullable(),
      oneClickLeadFormOpens: z.number().nullable(),
      landingPageClicks: z.number().nullable(),
      videoCompletions: z.number().nullable(),
      videoFirstQuartileCompletions: z.number().nullable(),
      videoMidpointCompletions: z.number().nullable(),
      videoThirdQuartileCompletions: z.number().nullable(),
      videoStarts: z.number().nullable(),
      externalWebsiteConversions: z.number().nullable(),
    }),
  }),
  contender: z.object({
    sponsoredCreativeId: z.string().uuid(),
    varientId: z.string().uuid(),
    varientsUsedInSponsoredCreative: varientsMap,
    metrics: z.object({
      impressions: z.number().nullable(),
      clicks: z.number().nullable(),
      conversions: z.number().nullable(),
      leads: z.number().nullable(),
      videoViews: z.number().nullable(),
      sends: z.number().nullable(),
      opens: z.number().nullable(),
      cost: z.number().nullable(),
      actionClicks: z.number().nullable(),
      totalEngagements: z.number().nullable(),
      oneClickLeadFormOpens: z.number().nullable(),
      landingPageClicks: z.number().nullable(),
      videoCompletions: z.number().nullable(),
      videoFirstQuartileCompletions: z.number().nullable(),
      videoMidpointCompletions: z.number().nullable(),
      videoThirdQuartileCompletions: z.number().nullable(),
      videoStarts: z.number().nullable(),
      externalWebsiteConversions: z.number().nullable(),
    }),
  }),
  winner: z.enum(["CURRENT_BEST", "CONTENDER"]).nullable(),
});

const upcomingAbTestRoundDataDtoSchema = baseAbTestRoundDataDtoSchema.extend({
  status: z.enum([
    "NOT_STARTED",
    "IN_PROGRESS",
    "FAILED",
    "CANCELLED",
    "COMPLETED",
    "AUTO_RESOLVED",
    "USER_RESOLVED",
  ]),
  currentBest: z.object({
    sponsoredCreativeId: z.string().uuid(),
    varientId: z.string().uuid(),
    adId: z.string(),
    adName: z.string(),
    varientsUsedInSponsoredCreative: varientsMap,
    metrics: z.object({
      impressions: z.number().nullable(),
      clicks: z.number().nullable(),
      conversions: z.number().nullable(),
      leads: z.number().nullable(),
      videoViews: z.number().nullable(),
      sends: z.number().nullable(),
      opens: z.number().nullable(),
      cost: z.number().nullable(),
      actionClicks: z.number().nullable(),
      totalEngagements: z.number().nullable(),
      oneClickLeadFormOpens: z.number().nullable(),
      landingPageClicks: z.number().nullable(),
      videoCompletions: z.number().nullable(),
      videoFirstQuartileCompletions: z.number().nullable(),
      videoMidpointCompletions: z.number().nullable(),
      videoThirdQuartileCompletions: z.number().nullable(),
      videoStarts: z.number().nullable(),
      externalWebsiteConversions: z.number().nullable(),
    }),
  }),
  contender: z.object({
    sponsoredCreativeId: z.string().uuid(),
    varientId: z.string().uuid(),
    adId: z.string(),
    adName: z.string(),
    varientsUsedInSponsoredCreative: varientsMap,
    metrics: z.object({
      impressions: z.number().nullable(),
      clicks: z.number().nullable(),
      conversions: z.number().nullable(),
      leads: z.number().nullable(),
      videoViews: z.number().nullable(),
      sends: z.number().nullable(),
      opens: z.number().nullable(),
      cost: z.number().nullable(),
      actionClicks: z.number().nullable(),
      totalEngagements: z.number().nullable(),
      oneClickLeadFormOpens: z.number().nullable(),
      landingPageClicks: z.number().nullable(),
      videoCompletions: z.number().nullable(),
      videoFirstQuartileCompletions: z.number().nullable(),
      videoMidpointCompletions: z.number().nullable(),
      videoThirdQuartileCompletions: z.number().nullable(),
      videoStarts: z.number().nullable(),
      externalWebsiteConversions: z.number().nullable(),
    }),
  }),
  winner: z.enum(["CURRENT_BEST", "CONTENDER"]).nullable(),
});

const abTestRoundDataDtoSchema = upcomingAbTestRoundDataDtoSchema;

export type AbTestRoundDataDto = z.infer<typeof abTestRoundDataDtoSchema>;
