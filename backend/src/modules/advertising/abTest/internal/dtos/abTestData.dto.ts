import { z } from "zod";

import { abTestTypeSchema } from "../domain/abTestType.valueObject";

const currentRunningAbTestDataDtoSchema = z.object({
  abTestId: z.string().uuid(),
  status: z.literal("IN_PROGRESS"),
  type: abTestTypeSchema,
  currentRound: z
    .object({
      id: z.string().uuid(),
      roundIndex: z.number(),
      currentBestId: z.string().uuid(),
      contenderId: z.string().uuid(),
    })
    .nullable(),
  pastRounds: z.array(
    z.object({
      id: z.string().uuid(),
      roundIndex: z.number(),
      currentBestId: z.string().uuid(),
      contenderId: z.string().uuid(),
      winner: z.enum(["CURRENT_BEST", "CONTENDER"]),
    }),
  ),
  upcomingRounds: z.array(
    z.object({
      id: z.string().uuid(),
      roundIndex: z.number(),
      currentBestId: z.string().uuid(),
      contenderId: z.string().uuid(),
    }),
  ),
});

const abTestDataDtoSchema = z.discriminatedUnion("status", [
  currentRunningAbTestDataDtoSchema,
]);

export type AbTestDataDto = z.infer<typeof abTestDataDtoSchema>;
