import { IStageRepository } from "../../../../application/interfaces/infrastructure/repositories/stage.repository.interface";
import { AbTest } from "../../domain/abTest.entity";
import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { GetAbTestsQuery } from "./getAbTests.query.interface";

export class GetAbTestsQueryHandler {
  constructor(private readonly abTestRepository: IAbTestRepository) {}
  async execute(query: GetAbTestsQuery): Promise<
    {
      stageId: string;
      type: AbTest["type"];
      status: "PAST" | "CURRENT" | "UPCOMING";
    }[]
  > {
    return this.abTestRepository.getAbTestsForAdSegment(query.adSegmentId);
  }
}
