import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { LinkedInService } from "../../../../infrastructure/services/linkedIn.service";
import { AbTestType } from "../../domain/abTestType.valueObject";

export interface GetAbTestRoundDataCommand {
  abTestRoundId: string;
  adSegmentId: string;
  type: AbTestType;
  tx: ITransaction;
}
