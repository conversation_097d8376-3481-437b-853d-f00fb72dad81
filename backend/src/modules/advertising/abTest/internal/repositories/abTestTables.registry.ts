import {
  IAbTestRoundDayMetricsTable,
  IAbTestRoundDayTable,
  IAbTestRoundTable,
  IAbTestTable,
} from "../../../../../database/schemas/advertising/abTests/_baseAbTestTableSchemaUtils";
import { IAdSegmentBestVariantTable } from "../../../../../database/schemas/advertising/abTests/_baseAdSegmentBestVariantUtils";
import {
  audienceTestRoundDayMetricsTable,
  audienceTestRoundDayTable,
  audienceTestRoundTable,
  audienceTestTable,
  conversationCallToActionTestRoundDayMetricsTable,
  conversationCallToActionTestRoundDayTable,
  conversationCallToActionTestRoundTable,
  conversationCallToActionTestTable,
  conversationMessageCopyTestRoundDayMetricsTable,
  conversationMessageCopyTestRoundDayTable,
  conversationMessageCopyTestRoundTable,
  conversationMessageCopyTestTable,
  conversationSubjectTestRoundDayMetricsTable,
  conversationSubjectTestRoundDayTable,
  conversationSubjectTestRoundTable,
  conversationSubjectTestTable,
  creativeTestRoundDayMetricsTable,
  creativeTestRoundDayTable,
  creativeTestRoundTable,
  creativeTestTable,
  socialPostBodyCopyTestRoundDayMetricsTable,
  socialPostBodyCopyTestRoundDayTable,
  socialPostBodyCopyTestRoundTable,
  socialPostBodyCopyTestTable,
  socialPostCallToActionTestRoundDayMetricsTable,
  socialPostCallToActionTestRoundDayTable,
  socialPostCallToActionTestRoundTable,
  socialPostCallToActionTestTable,
  valuePropTestRoundDayMetricsTable,
  valuePropTestRoundDayTable,
  valuePropTestRoundTable,
  valuePropTestTable,
} from "../../../../../database/schemas/advertising/abTests/abTestTables";
import {
  adSegmentBestAudienceVariantTable,
  adSegmentBestConversationCallToActionCopyVariantTable,
  adSegmentBestConversationMessageCopyVariantTable,
  adSegmentBestConversationSubjectVariantTable,
  adSegmentBestCreativeVariantTable,
  adSegmentBestSocialPostBodyCopyVariantTable,
  adSegmentBestSocialPostCallToActionCopyVariantTable,
  adSegmentBestValuePropVariantTable,
} from "../../../../../database/schemas/advertising/abTests/adsegmentBestVariantTables";
import { AbTestType } from "../domain/abTestType.valueObject";

type AbTestRepositoryMap = {
  [K in AbTestType]: {
    abTestTable: IAbTestTable;
    abTestRoundTable: IAbTestRoundTable;
    abTestRoundDayTable: IAbTestRoundDayTable;
    bestVariantTable: IAdSegmentBestVariantTable;
    abTestRoundDayMetricsTable: IAbTestRoundDayMetricsTable;
  };
};
export const abTestTableRegistry: AbTestRepositoryMap = {
  audience: {
    abTestTable: audienceTestTable,
    abTestRoundTable: audienceTestRoundTable,
    abTestRoundDayTable: audienceTestRoundDayTable,
    bestVariantTable: adSegmentBestAudienceVariantTable,
    abTestRoundDayMetricsTable: audienceTestRoundDayMetricsTable,
  },
  valueProp: {
    abTestTable: valuePropTestTable,
    abTestRoundTable: valuePropTestRoundTable,
    abTestRoundDayTable: valuePropTestRoundDayTable,
    bestVariantTable: adSegmentBestValuePropVariantTable,
    abTestRoundDayMetricsTable: valuePropTestRoundDayMetricsTable,
  },
  creative: {
    abTestTable: creativeTestTable,
    abTestRoundTable: creativeTestRoundTable,
    abTestRoundDayTable: creativeTestRoundDayTable,
    bestVariantTable: adSegmentBestCreativeVariantTable,
    abTestRoundDayMetricsTable: creativeTestRoundDayMetricsTable,
  },
  conversationSubject: {
    abTestTable: conversationSubjectTestTable,
    abTestRoundTable: conversationSubjectTestRoundTable,
    abTestRoundDayTable: conversationSubjectTestRoundDayTable,
    bestVariantTable: adSegmentBestConversationSubjectVariantTable,
    abTestRoundDayMetricsTable: conversationSubjectTestRoundDayMetricsTable,
  },
  conversationMessageCopy: {
    abTestTable: conversationMessageCopyTestTable,
    abTestRoundTable: conversationMessageCopyTestRoundTable,
    abTestRoundDayTable: conversationMessageCopyTestRoundDayTable,
    bestVariantTable: adSegmentBestConversationMessageCopyVariantTable,
    abTestRoundDayMetricsTable: conversationMessageCopyTestRoundDayMetricsTable,
  },
  conversationCallToAction: {
    abTestTable: conversationCallToActionTestTable,
    abTestRoundTable: conversationCallToActionTestRoundTable,
    abTestRoundDayTable: conversationCallToActionTestRoundDayTable,
    bestVariantTable: adSegmentBestConversationCallToActionCopyVariantTable,
    abTestRoundDayMetricsTable:
      conversationCallToActionTestRoundDayMetricsTable,
  },
  socialPostBodyCopy: {
    abTestTable: socialPostBodyCopyTestTable,
    abTestRoundTable: socialPostBodyCopyTestRoundTable,
    abTestRoundDayTable: socialPostBodyCopyTestRoundDayTable,
    bestVariantTable: adSegmentBestSocialPostBodyCopyVariantTable,
    abTestRoundDayMetricsTable: socialPostBodyCopyTestRoundDayMetricsTable,
  },
  socialPostCallToAction: {
    abTestTable: socialPostCallToActionTestTable,
    abTestRoundTable: socialPostCallToActionTestRoundTable,
    abTestRoundDayTable: socialPostCallToActionTestRoundDayTable,
    bestVariantTable: adSegmentBestSocialPostCallToActionCopyVariantTable,
    abTestRoundDayMetricsTable: socialPostCallToActionTestRoundDayMetricsTable,
  },
};
