import { api } from "@/trpc/client";
import { <PERSON>, Eye, Trophy } from "lucide-react";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@kalos/ui/accordion";
import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";

import { AbTestRoundDayMetrics } from "../../../../../../../../../backend/src/modules/advertising/abTest/internal/domain/abTestRoundDayMetrics.entity";
import { AbTestType } from "../../../../../../../../../backend/src/modules/advertising/abTest/internal/domain/abTestType.valueObject";

interface AbTestRoundsDetailsModalProps {
  abTestId: string;
  abTestType: AbTestType;
  adSegmentId: string;
}

// Component for individual round that fetches its own data
function RoundDetailsCard({
  roundId,
  roundIndex,
  adSegmentId,
  abTestType,
  status,
  winner,
}: {
  roundId: string;
  roundIndex: number;
  adSegmentId: string;
  abTestType: AbTestType;
  status: "past" | "current" | "upcoming";
  winner?: "CURRENT_BEST" | "CONTENDER";
}) {
  console.log("RoundDetailsCard", {
    roundId,
    roundIndex,
    adSegmentId,
    abTestType,
    status,
    winner,
  });
  // Fetch round data for current and past rounds
  const roundDataQuery =
    api.v2.ads.newAbTestController.getAbTestRoundData.useQuery({
      abTestRoundId: roundId,
      adSegmentId: adSegmentId,
      type: abTestType,
    });

  // Helper functions

  const getAdId = (variant: any) => {
    if (!variant) return "-";
    return variant.sponsoredCreativeId.slice(-8);
  };

  // Render different states based on round status
  if (status === "upcoming") {
    if (roundDataQuery.isLoading) {
      return (
        <div className="space-y-4">
          <div className="flex items-center space-x-2 border-b pb-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            <h3 className="text-lg font-semibold text-gray-900">
              Round #{roundIndex}
            </h3>
          </div>
          <div className="rounded-lg bg-gray-50 p-4">
            <div className="text-center text-gray-500">
              Loading round data...
            </div>
          </div>
        </div>
      );
    }

    if (roundDataQuery.isError || !roundDataQuery.data) {
      console.log("Error loading round data", roundDataQuery.error);
      return (
        <div className="space-y-4">
          <div className="flex items-center space-x-2 border-b pb-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            <h3 className="text-lg font-semibold text-gray-900">
              Round #{roundIndex}
            </h3>
          </div>
          <div className="rounded-lg bg-gray-50 p-4">
            <div className="text-center text-red-500">
              Error loading round data
            </div>
          </div>
        </div>
      );
    }

    const roundData = roundDataQuery.data;
    const winnerData =
      winner === "CURRENT_BEST" ? roundData.currentBest : roundData.contender;
    const loserData =
      winner === "CURRENT_BEST" ? roundData.contender : roundData.currentBest;
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2 border-b pb-2">
          <div className="flex h-5 w-5 items-center justify-center rounded-full bg-gray-300">
            <div className="h-2 w-2 rounded-full bg-white"></div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">
            Round #{roundIndex}
          </h3>
          <Badge variant="secondary">Upcoming</Badge>
        </div>

        <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
          <div className="mb-4 grid grid-cols-3 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
            <div>Winner</div>
            <div>Ad Name</div>
            <div>AD ID</div>
          </div>

          <div className="space-y-3">
            <div className="grid grid-cols-3 items-center gap-4 py-2">
              <div className="text-sm text-gray-400">TBD</div>
              <div className="text-gray-700">
                Winner of Round #{roundIndex - 1}
              </div>
              <div className="font-mono text-blue-600">-</div>
            </div>

            <div className="grid grid-cols-3 items-center gap-4 border-t border-gray-200 py-2">
              <div className="text-sm text-gray-400">TBD</div>
              <div className="text-gray-700">{roundData.contender.adName}</div>
              <div className="font-mono text-blue-600">
                {roundData.contender.adId.split(":").pop()}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (status === "past") {
    if (roundDataQuery.isLoading) {
      return (
        <div className="space-y-4">
          <div className="flex items-center space-x-2 border-b pb-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            <h3 className="text-lg font-semibold text-gray-900">
              Round #{roundIndex}
            </h3>
          </div>
          <div className="rounded-lg bg-gray-50 p-4">
            <div className="text-center text-gray-500">
              Loading round data...
            </div>
          </div>
        </div>
      );
    }

    if (roundDataQuery.isError || !roundDataQuery.data) {
      return (
        <div className="space-y-4">
          <div className="flex items-center space-x-2 border-b pb-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            <h3 className="text-lg font-semibold text-gray-900">
              Round #{roundIndex}
            </h3>
          </div>
          <div className="rounded-lg bg-gray-50 p-4">
            <div className="text-center text-red-500">
              Error loading round data
            </div>
          </div>
        </div>
      );
    }

    const roundData = roundDataQuery.data;
    const winnerData =
      winner === "CURRENT_BEST" ? roundData.currentBest : roundData.contender;
    const loserData =
      winner === "CURRENT_BEST" ? roundData.contender : roundData.currentBest;

    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2 border-b pb-2">
          <Trophy className="h-5 w-5 text-yellow-500" />
          <h3 className="text-lg font-semibold text-gray-900">
            Round #{roundIndex}
          </h3>
          <Badge variant="secondary">Past</Badge>
        </div>
        <div className="rounded-lg bg-gray-50 p-4">
          <div className="mb-4 grid grid-cols-3 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
            <div>Winner</div>
            <div>Ad Name</div>
            <div>AD ID</div>
          </div>

          <div className="space-y-3">
            <div className="grid grid-cols-3 items-center gap-4 py-2">
              <div className="flex items-center">
                <Badge className="border-green-200 bg-green-100 text-green-800">
                  <Crown className="mr-1 h-3 w-3" />
                  Winner
                </Badge>
              </div>
              <div className="font-medium text-gray-900">
                {winnerData.adName}
              </div>
              <div className="font-mono text-blue-600">
                {winnerData.adId.split(":").pop()}
              </div>
            </div>

            <div className="grid grid-cols-3 items-center gap-4 border-t border-gray-200 py-2">
              <div></div>
              <div className="text-gray-700">{loserData.adName} (Lost)</div>
              <div className="font-mono text-blue-600">
                {loserData.adId.split(":").pop()}
              </div>
            </div>
          </div>
        </div>
        <RoundDayMetrics roundDayMetrics={roundData.roundDayMetrics} />
      </div>
    );
  }

  // Current round
  if (roundDataQuery.isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2 border-b pb-2">
          <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-500">
            <div className="h-2 w-2 animate-pulse rounded-full bg-white"></div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">
            Round #{roundIndex}
          </h3>
          <Badge variant="outline" className="border-blue-200 text-blue-600">
            Current
          </Badge>
        </div>
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="text-center text-gray-500">Loading round data...</div>
        </div>
      </div>
    );
  }

  if (roundDataQuery.isError || !roundDataQuery.data) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2 border-b pb-2">
          <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-500">
            <div className="h-2 w-2 animate-pulse rounded-full bg-white"></div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">
            Round #{roundIndex}
          </h3>
          <Badge variant="outline" className="border-blue-200 text-blue-600">
            Current
          </Badge>
        </div>
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="text-center text-red-500">
            Error loading round data
          </div>
        </div>
      </div>
    );
  }

  const roundData = roundDataQuery.data;

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 border-b pb-2">
        <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-500">
          <div className="h-2 w-2 animate-pulse rounded-full bg-white"></div>
        </div>
        <h3 className="text-lg font-semibold text-gray-900">
          Round #{roundIndex}
        </h3>
        <Badge variant="outline" className="border-blue-200 text-blue-600">
          Current
        </Badge>
      </div>

      <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
        <div className="mb-4 grid grid-cols-3 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
          <div>Winner</div>
          <div>Ad Name</div>
          <div>AD ID</div>
        </div>

        <div className="space-y-3">
          <div className="grid grid-cols-3 items-center gap-4 py-2">
            <div className="text-sm text-gray-500">In Progress</div>
            <div className="font-medium text-gray-900">
              {roundData.currentBest.adName}
            </div>
            <div className="font-mono text-blue-600">
              {roundData.currentBest.adId.split(":").pop()}
            </div>
          </div>

          <div className="grid grid-cols-3 items-center gap-4 border-t border-gray-200 py-2">
            <div className="text-sm text-gray-500">In Progress</div>
            <div className="text-gray-700">{roundData.contender.adName}</div>
            <div className="font-mono text-blue-600">
              {roundData.contender.adId.split(":").pop()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function AbTestRoundsDetailsModal({
  abTestId,
  abTestType,
  adSegmentId,
}: AbTestRoundsDetailsModalProps) {
  const abTestQuery = api.v2.ads.newAbTestController.getAbTestData.useQuery({
    abTestId: abTestId,
    type: abTestType,
    adSegmentId: adSegmentId,
  });

  // Get rounds data from abTestData
  const pastRoundsData = abTestQuery.data?.pastRounds || [];
  const currentRoundData = abTestQuery.data?.currentRound;
  const upcomingRoundsData = abTestQuery.data?.upcomingRounds || [];

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Eye className="mr-2 h-4 w-4" />
          View Details
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Test Rounds Details
          </DialogTitle>
          <DialogDescription>
            Detailed breakdown of A/B test rounds and their performance
          </DialogDescription>
        </DialogHeader>

        <div className="mt-6 space-y-6">
          {/* Past Rounds */}
          {pastRoundsData.map((round) => (
            <RoundDetailsCard
              key={`past-${round.id}`}
              roundId={round.id}
              roundIndex={round.roundIndex}
              adSegmentId={adSegmentId}
              abTestType={abTestType}
              status="past"
              winner={round.winner}
            />
          ))}

          {/* Current Round */}
          {currentRoundData && (
            <RoundDetailsCard
              roundId={currentRoundData.id}
              roundIndex={currentRoundData.roundIndex}
              adSegmentId={adSegmentId}
              abTestType={abTestType}
              status="current"
            />
          )}

          {/* Upcoming Rounds */}
          {upcomingRoundsData.map((round) => (
            <RoundDetailsCard
              key={`upcoming-${round.id}`}
              roundId={round.id}
              roundIndex={round.roundIndex}
              adSegmentId={adSegmentId}
              abTestType={abTestType}
              status="upcoming"
            />
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export function RoundDayMetrics({
  roundDayMetrics,
}: {
  roundDayMetrics: AbTestRoundDayMetrics[];
}) {
  return (
    <div className="flex flex-col justify-start space-y-4">
      <Accordion type="single">
        {roundDayMetrics.map((dayMetrics) => (
          <AccordionItem
            key={dayMetrics.abTestRoundDayId}
            value={dayMetrics.abTestRoundDayId}
          >
            <AccordionTrigger>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-5 w-5 rounded-full bg-gray-300">
                    <div className="h-2 w-2 rounded-full bg-white"></div>
                  </div>
                  <div className="text-sm font-medium text-gray-900">
                    Day {dayMetrics.dayIndex}
                  </div>
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="rounded-t-lg border-l border-r border-t border-blue-200 p-4">
                <div className="grid grid-cols-3 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
                  <div>Metric</div>
                  <div>Current Best</div>
                  <div>Contender</div>
                </div>

                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 py-2">
                    <div className="font-medium text-gray-900">
                      Primary metric for day
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.currentBestMetrics.primaryMetric.forDay}
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.contenderMetrics.primaryMetric.forDay}
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 border-t py-2">
                    <div className="font-medium text-gray-900">
                      Secondary metric for day
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.currentBestMetrics.secondaryMetric.forDay}
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.contenderMetrics.secondaryMetric.forDay}
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 border-t  py-2">
                    <div className="font-medium text-gray-900">
                      Primary metric sum
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.currentBestMetrics.primaryMetric.sum}
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.contenderMetrics.primaryMetric.sum}
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 border-t  py-2">
                    <div className="font-medium text-gray-900">
                      Secondary metric sum
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.currentBestMetrics.secondaryMetric.sum}
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.contenderMetrics.secondaryMetric.sum}
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 border-t  py-2">
                    <div className="font-medium text-gray-900">
                      Primary metric mean
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.currentBestMetrics.primaryMetric.mean}
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.contenderMetrics.primaryMetric.mean}
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 border-t  py-2">
                    <div className="font-medium text-gray-900">
                      Primary metric mean
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.currentBestMetrics.secondaryMetric.mean}
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.contenderMetrics.secondaryMetric.mean}
                    </div>
                  </div>
                </div>
              </div>
              <div className="rounded-lg-b border border-l-blue-200 border-r-blue-200  bg-blue-50 p-4">
                <div className="grid grid-cols-2 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
                  <div>T-Test Results</div>
                </div>
                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 py-2">
                    <div className="font-medium text-gray-900">
                      Primary Metric T-Test Result
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.primaryMetricTTestResult ?? "-"}
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 border-t py-2">
                    <div className="font-medium text-gray-900">
                      Secondary Metric T-Test Result
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.secondaryMetricTTestResult ?? "-"}
                    </div>
                  </div>
                </div>
              </div>
              <div className="rounded-lg-b border border-b-blue-200 border-l-blue-200 border-r-blue-200  bg-green-50 p-4">
                <div className="grid grid-cols-2 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
                  <div>Decision</div>
                </div>

                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 border-t  py-2">
                    <div className="font-medium text-gray-900">
                      Decision Reason
                    </div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.decisionType
                        .toLowerCase()
                        .replaceAll("_", " ")
                        .replace(/^\w/, (c) => c.toUpperCase())}
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 border-t  py-2">
                    <div className="font-medium text-gray-900">Winner</div>
                    <div className="font-medium text-gray-900">
                      {dayMetrics.decision
                        .toLowerCase()
                        .replaceAll("_", " ")
                        .replace(/^\w/, (c) => c.toUpperCase())}
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 border-t  py-2">
                    <div className="font-medium text-gray-900">Datetime</div>
                    <div className="space-x-2 font-medium text-gray-900">
                      {dayMetrics.createdAt.toLocaleString()}
                    </div>
                    <div className="font-medium text-gray-900"></div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
}
