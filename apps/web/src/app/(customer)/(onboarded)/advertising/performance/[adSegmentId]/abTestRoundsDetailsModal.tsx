import { api } from "@/trpc/client";
import { <PERSON>, Eye, Trophy } from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import { <PERSON><PERSON> } from "@kalos/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";

import { AbTestType } from "../../../../../../../../../backend/src/modules/advertising/abTest/internal/domain/abTestType.valueObject";

interface AbTestRoundsDetailsModalProps {
  abTestId: string;
  abTestType: AbTestType;
  adSegmentId: string;
}

// Component for individual round that fetches its own data
function RoundDetailsCard({
  roundId,
  roundIndex,
  adSegmentId,
  abTestType,
  status,
  winner,
}: {
  roundId: string;
  roundIndex: number;
  adSegmentId: string;
  abTestType: AbTestType;
  status: "past" | "current" | "upcoming";
  winner?: "CURRENT_BEST" | "CONTENDER";
}) {
  // Fetch round data for current and past rounds
  const roundDataQuery =
    api.v2.ads.newAbTestController.getAbTestRoundData.useQuery(
      {
        abTestRoundId: roundId,
        adSegmentId: adSegmentId,
        type: abTestType,
      },
      {
        enabled: status === "current" || status === "past", // Only fetch data for current and past rounds
      },
    );

  // Helper functions

  const getAdId = (variant: any) => {
    if (!variant) return "-";
    return variant.sponsoredCreativeId.slice(-8);
  };

  // Render different states based on round status
  if (status === "upcoming") {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2 border-b pb-2">
          <div className="flex h-5 w-5 items-center justify-center rounded-full bg-gray-300">
            <div className="h-2 w-2 rounded-full bg-white"></div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">
            Round #{roundIndex}
          </h3>
          <Badge variant="secondary">Upcoming</Badge>
        </div>

        <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
          <div className="mb-4 grid grid-cols-3 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
            <div>Winner</div>
            <div>Ad Name</div>
            <div>AD ID</div>
          </div>

          <div className="space-y-3">
            <div className="grid grid-cols-3 items-center gap-4 py-2">
              <div className="text-sm text-gray-400">TBD</div>
              <div className="text-gray-700">Winner of Round #{roundIndex}</div>
              <div className="font-mono text-blue-600">-</div>
            </div>

            <div className="grid grid-cols-3 items-center gap-4 border-t border-gray-200 py-2">
              <div className="text-sm text-gray-400">TBD</div>
              <div className="text-gray-700">New Challenger</div>
              <div className="font-mono text-blue-600">-</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (status === "past") {
    if (roundDataQuery.isLoading) {
      return (
        <div className="space-y-4">
          <div className="flex items-center space-x-2 border-b pb-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            <h3 className="text-lg font-semibold text-gray-900">
              Round #{roundIndex}
            </h3>
          </div>
          <div className="rounded-lg bg-gray-50 p-4">
            <div className="text-center text-gray-500">
              Loading round data...
            </div>
          </div>
        </div>
      );
    }

    if (roundDataQuery.isError || !roundDataQuery.data) {
      return (
        <div className="space-y-4">
          <div className="flex items-center space-x-2 border-b pb-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            <h3 className="text-lg font-semibold text-gray-900">
              Round #{roundIndex}
            </h3>
          </div>
          <div className="rounded-lg bg-gray-50 p-4">
            <div className="text-center text-red-500">
              Error loading round data
            </div>
          </div>
        </div>
      );
    }

    const roundData = roundDataQuery.data;
    const winnerData =
      winner === "CURRENT_BEST" ? roundData.currentBest : roundData.contender;
    const loserData =
      winner === "CURRENT_BEST" ? roundData.contender : roundData.currentBest;

    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2 border-b pb-2">
          <Trophy className="h-5 w-5 text-yellow-500" />
          <h3 className="text-lg font-semibold text-gray-900">
            Round #{roundIndex}
          </h3>
        </div>

        <div className="rounded-lg bg-gray-50 p-4">
          <div className="mb-4 grid grid-cols-3 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
            <div>Winner</div>
            <div>Ad Name</div>
            <div>AD ID</div>
          </div>

          <div className="space-y-3">
            <div className="grid grid-cols-3 items-center gap-4 py-2">
              <div className="flex items-center">
                <Badge className="border-green-200 bg-green-100 text-green-800">
                  <Crown className="mr-1 h-3 w-3" />
                  Winner
                </Badge>
              </div>
              <div className="font-medium text-gray-900">winnerData.adName</div>
              <div className="font-mono text-blue-600">
                {getAdId(winnerData)}
              </div>
            </div>

            <div className="grid grid-cols-3 items-center gap-4 border-t border-gray-200 py-2">
              <div></div>
              <div className="text-gray-700">loserData.adName (Lost)</div>
              <div className="font-mono text-blue-600">
                {getAdId(loserData)}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Current round
  if (roundDataQuery.isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2 border-b pb-2">
          <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-500">
            <div className="h-2 w-2 animate-pulse rounded-full bg-white"></div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">
            Round #{roundIndex}
          </h3>
          <Badge variant="outline" className="border-blue-200 text-blue-600">
            Current
          </Badge>
        </div>
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="text-center text-gray-500">Loading round data...</div>
        </div>
      </div>
    );
  }

  if (roundDataQuery.isError || !roundDataQuery.data) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2 border-b pb-2">
          <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-500">
            <div className="h-2 w-2 animate-pulse rounded-full bg-white"></div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">
            Round #{roundIndex}
          </h3>
          <Badge variant="outline" className="border-blue-200 text-blue-600">
            Current
          </Badge>
        </div>
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="text-center text-red-500">
            Error loading round data
          </div>
        </div>
      </div>
    );
  }

  const roundData = roundDataQuery.data;

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 border-b pb-2">
        <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-500">
          <div className="h-2 w-2 animate-pulse rounded-full bg-white"></div>
        </div>
        <h3 className="text-lg font-semibold text-gray-900">
          Round #{roundIndex}
        </h3>
        <Badge variant="outline" className="border-blue-200 text-blue-600">
          Current
        </Badge>
      </div>

      <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
        <div className="mb-4 grid grid-cols-3 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
          <div>Winner</div>
          <div>Ad Name</div>
          <div>AD ID</div>
        </div>

        <div className="space-y-3">
          <div className="grid grid-cols-3 items-center gap-4 py-2">
            <div className="text-sm text-gray-500">In Progress</div>
            <div className="font-medium text-gray-900">
              {roundData.currentBest.adName}
            </div>
            <div className="font-mono text-blue-600">
              {getAdId(roundData.currentBest)}
            </div>
          </div>

          <div className="grid grid-cols-3 items-center gap-4 border-t border-gray-200 py-2">
            <div className="text-sm text-gray-500">In Progress</div>
            <div className="text-gray-700">{roundData.contender.adName}</div>
            <div className="font-mono text-blue-600">
              {getAdId(roundData.contender)}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function AbTestRoundsDetailsModal({
  abTestId,
  abTestType,
  adSegmentId,
}: AbTestRoundsDetailsModalProps) {
  const abTestQuery = api.v2.ads.newAbTestController.getAbTestData.useQuery({
    abTestId: abTestId,
    type: abTestType,
    adSegmentId: adSegmentId,
  });

  // Get rounds data from abTestData
  const pastRoundsData = abTestQuery.data?.pastRounds || [];
  const currentRoundData = abTestQuery.data?.currentRound;
  const upcomingRoundsData = abTestQuery.data?.upcomingRounds || [];

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Eye className="mr-2 h-4 w-4" />
          View Details
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Test Rounds Details
          </DialogTitle>
          <DialogDescription>
            Detailed breakdown of A/B test rounds and their performance
          </DialogDescription>
        </DialogHeader>

        <div className="mt-6 space-y-6">
          {/* Past Rounds */}
          {pastRoundsData.map((round) => (
            <RoundDetailsCard
              key={`past-${round.id}`}
              roundId={round.id}
              roundIndex={round.roundIndex}
              adSegmentId={adSegmentId}
              abTestType={abTestType}
              status="past"
              winner={round.winner}
            />
          ))}

          {/* Current Round */}
          {currentRoundData && (
            <RoundDetailsCard
              roundId={currentRoundData.id}
              roundIndex={currentRoundData.roundIndex}
              adSegmentId={adSegmentId}
              abTestType={abTestType}
              status="current"
            />
          )}

          {/* Upcoming Rounds */}
          {upcomingRoundsData.map((round) => (
            <RoundDetailsCard
              key={`upcoming-${round.id}`}
              roundId={round.id}
              roundIndex={round.roundIndex}
              adSegmentId={adSegmentId}
              abTestType={abTestType}
              status="upcoming"
            />
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}
