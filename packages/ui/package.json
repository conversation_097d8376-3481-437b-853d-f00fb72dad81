{"name": "@kalos/ui", "version": "0.1.0", "private": true, "type": "module", "license": "MIT", "exports": {"./accordion": "./src/accordion.tsx", "./select": "./src/select.tsx", "./index": "./src/index.ts", "./navbar": "./src/nav-bar.tsx", "./button": "./src/button.tsx", "./badge": "./src/badge.tsx", "./card": "./src/card.tsx", "./chip": "./src/chip.tsx", "./command": "./src/command.tsx", "./form": "./src/form.tsx", "./dialog": "./src/dialog.tsx", "./seperator": "./src/seperator.tsx", "./popover": "./src/popover.tsx", "./breadcrumb": "./src/breadcrumb.tsx", "./skeleton": "./src/skeleton.tsx", "./icons/*": "./src/icons/*.tsx", "./table/datatable": "./src/table/data-table.tsx", "./table/factedfilter": "./src/table/facted-filter.tsx", "./input": "./src/input.tsx", "./label": "./src/label.tsx", "./textarea": "./src/textarea.tsx", "./calender": "./src/calender.tsx", "./radiogroup": "./src/radio-group.tsx", "./checkbox": "./src/checkbox.tsx", "./table/table": "./src/table/table.tsx", "./switch": "./src/switch.tsx", "./money-input": "./src/money-input.tsx", "./lottie": "./src/lottie.tsx", "./tooltip": "./src/tooltip.tsx", "./alert": "./src/alert.tsx", "./tabs": "./src/tabs.tsx", "./progress": "./src/progress.tsx"}, "scripts": {"add": "pnpm dlx shadcn-ui add", "clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "dependencies": {"@lottiefiles/dotlottie-react": "^0.13.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-table": "^8.17.3", "class-variance-authority": "^0.7.0", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "react-day-picker": "^8.9.1", "react-hook-form": "^7.52.1", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.0"}, "peerDependencies": {"react": "^18", "zod": "^3.23.0"}, "devDependencies": {"@kalos/eslint-config": "workspace:*", "@kalos/prettier-config": "workspace:*", "@kalos/tailwind-config": "workspace:*", "@kalos/tsconfig": "workspace:*", "eslint": "^9.0.0", "prettier": "^3.2.5", "react": "^18", "tailwindcss": "^3.4.1", "typescript": "^5.4.5"}, "prettier": "@kalos/prettier-config"}